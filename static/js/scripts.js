document.addEventListener('DOMContentLoaded', () => {
    // Theme toggle functionality
    function initThemeToggle() {
        const themeToggleBtns = document.querySelectorAll('#theme-toggle');
        const darkIcons = document.querySelectorAll('#theme-toggle-dark-icon');
        const lightIcons = document.querySelectorAll('#theme-toggle-light-icon');

        // Check local storage for theme
        const storedTheme = localStorage.getItem('theme');

        function setTheme(isDark) {
            if (isDark) {
                document.documentElement.classList.add('dark');
                lightIcons.forEach(icon => icon.classList.remove('hidden'));
                darkIcons.forEach(icon => icon.classList.add('hidden'));
            } else {
                document.documentElement.classList.remove('dark');
                darkIcons.forEach(icon => icon.classList.remove('hidden'));
                lightIcons.forEach(icon => icon.classList.add('hidden'));
            }
        }

        // Initialize theme
        if (storedTheme === 'dark') {
            setTheme(true);
        } else if (storedTheme === 'light') {
            setTheme(false);
        } else if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
            // Default to system preference
            setTheme(true);
        } else {
            setTheme(false);
        }

        // Add click listeners to all theme toggle buttons
        themeToggleBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const isDark = document.documentElement.classList.contains('dark');
                if (isDark) {
                    localStorage.setItem('theme', 'light');
                    setTheme(false);
                } else {
                    localStorage.setItem('theme', 'dark');
                    setTheme(true);
                }
            });
        });
    }

    // Initialize theme toggle
    initThemeToggle();

    // Additional utility functions for forms and interactions
    window.gardenPlanner = {
        // Utility function to show loading state
        showLoading: function(element) {
            if (element) {
                element.disabled = true;
                element.innerHTML = '<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Loading...';
            }
        },

        // Utility function to hide loading state
        hideLoading: function(element, originalText) {
            if (element) {
                element.disabled = false;
                element.innerHTML = originalText || 'Submit';
            }
        },

        // Utility function to show toast notifications
        showToast: function(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg transition-all duration-300 ${
                type === 'success' ? 'bg-green-500 text-white' :
                type === 'error' ? 'bg-red-500 text-white' :
                type === 'warning' ? 'bg-yellow-500 text-black' :
                'bg-blue-500 text-white'
            }`;
            toast.textContent = message;
            document.body.appendChild(toast);

            // Auto remove after 5 seconds
            setTimeout(() => {
                toast.style.opacity = '0';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 5000);
        }
    };
});
